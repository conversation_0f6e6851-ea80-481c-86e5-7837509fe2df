# ============================
# Stage 1: Build Stage
# ============================
FROM node:lts-alpine AS build

WORKDIR /app

# Install build-time dependencies
RUN apk add --no-cache libc6-compat python3 make g++ git

# Copy package files first to leverage Docker cache
# Note: Using package-lock.json instead of yarn.lock
COPY package.json package-lock.json ./

# Install all dependencies (including devDependencies) using npm ci for reproducible builds
RUN npm ci

# Copy the rest of the source code
COPY . .

# Build the application
RUN npm run build

# Prune devDependencies to keep only production dependencies
# For npm v7+, --omit=dev is the modern equivalent of --production
RUN npm install --omit=dev --ignore-scripts && \
    npm cache clean --force
    
# ============================
# Stage 2: Production Stage
# ============================
FROM node:lts-alpine AS prod

WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache libc6-compat tzdata dumb-init git

# Set environment variables
ENV NODE_ENV=production \
    TZ="Asia/Makassar"

# Copy application files and node_modules from build stage
COPY --from=build /app /app

# Remove unnecessary directories to reduce image size
RUN rm -rf /app/src

# Set up directory structure and permissions
RUN mkdir -p /app/logs && chown -R node:node /app/logs

# Switch to non-root user
USER node

# Expose application port
EXPOSE 4000

# Use dumb-init as entrypoint for better signal handling
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Define the default command
CMD ["node", "dist/main.js"]