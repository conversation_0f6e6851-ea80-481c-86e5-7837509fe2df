# ============================
# Stage 1: Build Stage
# ============================
# Use specific Node.js version for consistency (matching our LTS v22.17.0)
FROM node:22.17.0-alpine AS build

# Set build arguments
ARG NODE_ENV=production
ARG BUILD_FLAG=""

WORKDIR /app

# Install build-time dependencies with specific versions for security
RUN apk add --no-cache \
    libc6-compat=1.2.4-r2 \
    python3=3.11.10-r0 \
    make=4.4.1-r2 \
    g++=12.2.1_git20220924-r10 \
    git=2.40.1-r0 \
    && rm -rf /var/cache/apk/*

# Create non-root user for build process
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy package files first to leverage Docker cache
COPY package.json package-lock.json ./

# Verify package-lock.json integrity and install dependencies
RUN npm ci --only=production --frozen-lockfile --no-audit --no-fund && \
    npm ci --frozen-lockfile --no-audit --no-fund

# Copy source code with proper ownership
COPY --chown=nextjs:nodejs . .

# Build the application with error handling
RUN npm run build && \
    npm prune --production && \
    npm cache clean --force
# ============================
# Stage 2: Production Stage
# ============================
FROM node:22.17.0-alpine AS production

# Install security updates and runtime dependencies
RUN apk update && apk upgrade && \
    apk add --no-cache \
    libc6-compat=1.2.4-r2 \
    tzdata=2024a-r1 \
    dumb-init=1.2.5-r2 \
    curl=8.5.0-r0 \
    && rm -rf /var/cache/apk/*

# Create non-root user with specific UID/GID
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

WORKDIR /app

# Set environment variables for production
ENV NODE_ENV=production \
    TZ="Asia/Makassar" \
    NODE_OPTIONS="--max-old-space-size=1024" \
    NPM_CONFIG_LOGLEVEL=warn \
    NPM_CONFIG_PROGRESS=false

# Copy only necessary files from build stage
COPY --from=build --chown=nextjs:nodejs /app/dist ./dist
COPY --from=build --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=build --chown=nextjs:nodejs /app/views ./views

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/uploads /tmp/app && \
    chown -R nextjs:nodejs /app/logs /app/uploads /tmp/app && \
    chmod 755 /app/logs /app/uploads /tmp/app

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:4000/health || exit 1

# Switch to non-root user
USER nextjs

# Expose application port
EXPOSE 4000

# Use dumb-init as entrypoint for better signal handling
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Define the default command with proper error handling
CMD ["node", "--unhandled-rejections=strict", "dist/main.js"]