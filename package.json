{"name": "siakadv4", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"seed": "node ./src/seeds/seed.js", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "node --loader ts-node/esm ./node_modules/typeorm/cli.js"}, "resolutions": {"string-width": "4.2.3"}, "dependencies": {"@casl/ability": "^6.7.3", "@daypilot/daypilot-lite-javascript": "^4.1.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/class-validator": "^0.13.4", "@nestjs/common": "^10.4.19", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.19", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.19", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "@saemhco/nestjs-html-pdf": "^0.1.0", "@types/cookie-parser": "^1.4.9", "@types/multer": "^1.4.13", "argon2": "^0.43.0", "bcrypt": "^5.1.1", "blob-stream": "^0.1.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "express-session": "^1.18.1", "handlebars": "^4.7.8", "hbs": "^4.2.0", "hbs-utils": "^0.0.4", "ioredis": "^5.6.1", "jsdoc": "^4.0.4", "multer": "^2.0.1", "mysql2": "^3.14.1", "nest-commander": "^3.17.0", "nestjs-seeder": "^0.3.2", "nodemailer": "^6.10.1", "particles.js": "^2.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf2json": "^3.1.6", "pdfkit": "^0.17.1", "pdfkit-table": "^0.1.99", "pdfmake": "^0.2.20", "pg": "^8.16.2", "pg-promise": "^11.14.0", "puppeteer": "^24.10.2", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "sanitize-html": "^2.17.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25", "wkhtmltopdf": "^0.4.0"}, "devDependencies": {"@nestjs/cli": "^10.4.7", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.19", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pdfkit": "^0.13.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "commander": "^12.1.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}