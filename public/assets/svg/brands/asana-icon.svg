<svg width="400" height="370" viewBox="0 0 400 370" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M313.011 195.752C264.96 195.752 226.014 234.698 226.014 282.741C226.014 330.788 264.96 369.743 313.011 369.743C361.054 369.743 400 330.788 400 282.741C400 234.698 361.054 195.752 313.011 195.752V195.752ZM86.9931 195.756C38.95 195.761 0 234.698 0 282.745C0 330.788 38.95 369.738 86.9931 369.738C135.04 369.738 173.99 330.788 173.99 282.745C173.99 234.698 135.04 195.756 86.9889 195.756H86.9931V195.756ZM286.993 87.0105C286.993 135.054 248.048 174.012 200.004 174.012C151.953 174.012 113.007 135.054 113.007 87.0105C113.007 38.9674 151.953 0.017395 200.004 0.017395C248.047 0.017395 286.989 38.9674 286.989 87.0105H286.993Z" fill="url(#paint0_radial)"/>
</g>
<defs>
<radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(200.054 202.311) scale(265.2 245.128)">
<stop stop-color="#FFB900"/>
<stop offset="0.6" stop-color="#F95D8F"/>
<stop offset="0.999" stop-color="#F95353"/>
</radialGradient>
<clipPath id="clip0">
<rect width="400" height="369.76" fill="white"/>
</clipPath>
</defs>
</svg>
