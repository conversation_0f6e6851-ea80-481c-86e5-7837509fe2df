import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { CreateKurikulumBerlakuDto } from './dto/create-kurikulum-berlaku.dto';
import { UpdateKurikulumBerlakuDto } from './dto/update-kurikulum-berlaku.dto';
import { KurikulumBerlaku } from './entities/kurikulum-berlaku.entity';
// Removed incorrect import - query is not exported from express
import { QueryKurikulumBerlakuDto } from './dto/query-kurikulum-berlaku.dto';
import { UpdateKurikulumFeeder } from './dto/update-kurikulum-feeder.dto';

@Injectable()
export class KurikulumBerlakuService {
  constructor(
    @InjectRepository(KurikulumBerlaku)
    private kurikulumBerlakuRepository: Repository<KurikulumBerlaku>,
    private dataSource: DataSource,
  ) {}

  // async findAll(query: any = {}): Promise<KurikulumBerlaku[]> {
  //   return this.kurikulumBerlakuRepository.find({
  //     relations: ['programStudi', 'kurikulum'],
  //     where: query,
  //   });
  // }

  async findAll(programStudiIds: string | string[] = []): Promise<KurikulumBerlaku[]> {
    let whereClause: any = undefined;
    console.log(programStudiIds);

    if (Array.isArray(programStudiIds) && programStudiIds.length > 0) {
      whereClause = { programStudi: { id: In(programStudiIds) } };
    } else if (typeof programStudiIds === 'string' && programStudiIds.trim() !== '') {
      whereClause = { programStudi: { id: programStudiIds } };
    }

    return this.kurikulumBerlakuRepository.find({
      relations: ['programStudi', 'kurikulum'],
      where: whereClause, 
    });
  }

  async findOne(id: string): Promise<KurikulumBerlaku> {
    return this.kurikulumBerlakuRepository.findOne({
      where: { id: id },
      relations: ['programStudi', 'kurikulum'],
    });
  }

  async create(createKurikulumBerlakuDto: CreateKurikulumBerlakuDto) {
    const kurikulumBerlaku = this.kurikulumBerlakuRepository.create(
      createKurikulumBerlakuDto,
    );
    const inserted = await this.kurikulumBerlakuRepository.insert(
      kurikulumBerlaku,
    );
    const created_id = inserted.identifiers[0].id;
    return this.findOne(created_id);
  }

  async update(
    id: string,
    updateKurikulumBerlakuDto: UpdateKurikulumBerlakuDto,
  ) {
    await this.kurikulumBerlakuRepository.update(id, updateKurikulumBerlakuDto);
    return this.findOne(id);
  }

  async updateMany(updateKurikulumBerlakuDtos: UpdateKurikulumBerlakuDto[]) {
    await this.kurikulumBerlakuRepository.save(updateKurikulumBerlakuDtos);
    return true;
  }

  async remove(id: string) {
    return this.kurikulumBerlakuRepository.delete(id);
  }

  async removeMany(ids: string[]) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      await queryRunner.manager.delete(KurikulumBerlaku, ids);
      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.log(err.message);

      return false;
    } finally {
      await queryRunner.release();
    }
    return true;
  }

  async list(query: any = {}) {
    const data = await this.findAll(query);
    let result = [];
    console.log(data);
    if (data.length > 0) {
      for (let item of data) {
        result.push({
          id: item.id,
          nama_kurikulum: item.kurikulum.nama,
          id_prodi: item.programStudi.id_feeder,
          id_semester: item.tahunAkademik.kode,
          program_studi: item.programStudi.nama,
          jumlah_sks_lulus: item.kurikulum.jumlah_sks,
          jumlah_sks_wajib: item.kurikulum.sks_wajib,
          jumlah_sks_pilihan: item.kurikulum.sks_pilihan,
          ref_feeder: item.ref_feeder,
        });
      }
    }

    return result;
  }

  async updateRefFeeder(body: UpdateKurikulumFeeder) {
    return await this.kurikulumBerlakuRepository.save({
      id: body.id,
      ref_feeder: body.ref_feeder,
    });
  }
}
