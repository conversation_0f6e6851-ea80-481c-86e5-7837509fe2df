import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { CreatePenilaianDto } from './dto/create-penilaian.dto';
import { UpdatePenilaianDto } from './dto/update-penilaian.dto';
import { AspekPenilaian } from './entities/aspek-penilaian.entity';
import { Penilaian } from './entities/penilaian.entity';
// Removed incorrect import - query is not exported from express
import { Enrollment } from '../enrollment/entities/enrollment.entity';

@Injectable()
export class PenilaianService {
  constructor(
    @InjectRepository(Penilaian)
    private penilaianRepository: Repository<Penilaian>,
    private dataSource: DataSource,
  ) {}

  async findAll(query = {}): Promise<Penilaian[]> {
    return this.penilaianRepository.find({
      where: query,
      relations: ['aspekPenilaian', 'enrollment'],
    });
  }

  async findOne(id: string): Promise<Penilaian> {
    return this.penilaianRepository.findOne({
      where: {
        id,
      },
      relations: [],
    });
  }

  async findOneWithRelations(
    id: string,
    relations: string[],
  ): Promise<Penilaian> {
    return this.penilaianRepository.findOne({
      where: {
        id,
      },
      relations: relations,
    });
  }

  async findAllWithRelations(relations: string[]): Promise<Penilaian[]> {
    return this.penilaianRepository.find({
      relations: relations,
    });
  }

  async create(createPenilaianDto: CreatePenilaianDto) {
    const penilaian = await this.penilaianRepository.create(createPenilaianDto);
    const inserted = await this.penilaianRepository.insert(penilaian);
    const created_id = inserted.identifiers[0].id;
    return this.findOne(created_id);
  }

  async update(id: string, updatePenilaianDto: UpdatePenilaianDto) {
    const penilaian = await this.findOne(id);
    if (penilaian.isLocked === true) return false;
    await this.penilaianRepository.update(id, updatePenilaianDto);
    return this.findOne(id);
  }

  async updateMany(updatePenilaianDtos: UpdatePenilaianDto[], enrollment: any) {
    await this.penilaianRepository.save(updatePenilaianDtos);

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    for (let _enrollment of enrollment) {
      await queryRunner.manager.update(
        Enrollment,
        { id: _enrollment.id },
        { nilai: _enrollment.nilai },
      );
    }
    await queryRunner.commitTransaction();

    return true;
  }

  async remove(id: string, softDelete = false) {
    if (softDelete === true) return this.penilaianRepository.softDelete(id);
    return this.penilaianRepository.delete(id);
  }

  async removeMany(ids: string[]) {
    const queryRunner = this.dataSource.createQueryRunner();
    let result;
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      result = await queryRunner.manager.delete(AspekPenilaian, ids);
      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.log(err.message);
    } finally {
      await queryRunner.release();
    }
    return result;
  }

  async locked(id: any, isLocked: string) {
    const queryBuilder = this.dataSource.createQueryBuilder();
    return queryBuilder
      .update(Penilaian)
      .set({
        isLocked: isLocked == 'true' ? true : false,
      })
      .whereInIds(id)
      .execute();
  }
}
