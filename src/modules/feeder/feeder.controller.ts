import { Get, Post, Controller, Body, Res, Param, Query, Put, Req, UseGuards, ParseIntPipe } from '@nestjs/common';
import { MahasiswaService } from '../mahasiswa/mahasiswa.service';
import { KurikulumService } from '../kurikulum/kurikulum.service';
import { MataKuliahService } from '../mata-kuliah/mata-kuliah.service';
import { CreateMahasiswaDto } from '../mahasiswa/dto/create-mahasiswa.dto';
import { CreateBiodataDto } from '../mahasiswa/dto/create-biodata.dto';
import { CreateRiwayatDto } from '../mahasiswa/dto/create-riwayat.dto';
import { CreateMataKuliahDto } from '../mata-kuliah/dto/create-mata-kuliah.dto';
import { CreateDosenDto } from '../dosen/dto/create-dosen.dto';
import { ResponseUtil } from '../../utils/index';
import { validateOrReject } from 'class-validator';
import { DosenService } from '../dosen/dosen.service';
import { CreateBulkMahasiswaDto } from '../mahasiswa/dto/create-bulk-mahasiswa.dto';
import { ApiTags } from '@nestjs/swagger';
import { ProgramStudiService } from '../program-studi/program-studi.service';
import { CreateProgramStudiDto } from '../program-studi/dto/create-program-studi.dto';
import { CreateTahunAkademikDto } from '../tahun-akademik/dto/create-tahun-akademik.dto';
import { TahunAkademikService } from '../tahun-akademik/tahun-akademik.service';
import { async, retry } from 'rxjs';
import { CreateKurikulumDto } from '../kurikulum/dto/create-kurikulum.dto';
import { KurikulumBerlakuService } from '../kurikulum/kurikulum-berlaku.service';
import { KelasService } from '../kelas/kelas.service';
import { KRSService } from '../krs/krs.service';
import { EnrollmentService } from '../enrollment/enrollment.service';
import { CreateKelasDto } from '../kelas/dto/create-kelas.dto';
import { CreateKelasFeederDto } from '../kelas/dto/create-kelas-feeder.dto';
import { CreateKRSFeederDto } from '../krs/dto/create-krs-feeder.dto';
// Removed incorrect import - query is not exported from express
import { QueryKurikulumBerlakuDto } from '../kurikulum/dto/query-kurikulum-berlaku.dto';
import { UpdateKurikulumFeeder } from '../kurikulum/dto/update-kurikulum-feeder.dto';
import { QueryMataKuliah } from '../mata-kuliah/dto/query-mata-kuliah.dto';
import { QueryKelasDto } from '../kelas/dto/query-kelas.dto';
import { RolesGuard } from '../auth/roles.guard';
import { Role } from 'src/enums/role.enum';
import { Roles } from '../auth/roles.decorator';
@Controller('feeder')
@ApiTags('Feeder')
export class FeederController {
    constructor(
        private readonly mahasiswaService: MahasiswaService,
        private readonly kurikulumService: KurikulumService,
        private readonly matakuliahService: MataKuliahService,
        private readonly dosenService: DosenService,
        private readonly programStudiService: ProgramStudiService,
        private readonly tahunAkademikService: TahunAkademikService,
        private readonly kurikulumBerlakuService: KurikulumBerlakuService,
        private readonly kelasService: KelasService,
        private readonly krsService: KRSService,
        private readonly enrollmentService: EnrollmentService
      ) {}

    @Get('index')
    @UseGuards(RolesGuard)
    @Roles(Role.Admin)
    async index(@Req() req, @Res() res) {
      console.log(req.sessionID);
      
      return res.redirect(`${process.env.FEEDER_URL}/callback/?sid=${req.sessionID}`)
    }

    @Get('mahasiswa/list')
    async listMahasiswa(
      @Query('page', new ParseIntPipe()) page: number=1,
      @Query('limit', new ParseIntPipe()) limit: number=10,
    ) :Promise<ResponseUtil> {
      try {
          const result = await this.mahasiswaService.mappingFeeder(page, limit);
          return ResponseUtil.successWithPaginate(page, result.total, result.data)
      } catch (error) {
        return ResponseUtil.failed(error);
      }
    }

    @Post('mahasiswa')
    async storeMahasiswa(@Body() createMahasiswaDto: CreateMahasiswaDto[]) {
      try {
        const result = await this.mahasiswaService.store(createMahasiswaDto);
        if (result instanceof Error) {
          return ResponseUtil.failed(result.message);
        }
        return ResponseUtil.success();
      } catch (error) {
        return ResponseUtil.failed(error);
      }
    }

    @Get('kurikulum/list')
    async getKurikulum(@Query() query : QueryKurikulumBerlakuDto) {
      try {
        const result = await this.kurikulumBerlakuService.list(query)

        return ResponseUtil.success(result);
      } catch (error) {
        return ResponseUtil.failed(error);
      }
    }

    @Get('matakuliah/list')
    async listMatakuliah(@Query() query: QueryMataKuliah) {
      try {
        const result = await this.matakuliahService.list(query)
        console.log(result)
        return ResponseUtil.success(result)
      } catch(error) {
        console.log(error)
        return ResponseUtil.failed(error)
      }
    }

    @Post('matakuliah')
    async storeMatakuliah(@Body() createMatakuliahDto: CreateMataKuliahDto[]) {
      try {
        const result = await this.matakuliahService.store(createMatakuliahDto)
        return ResponseUtil.success()
      } catch(error) {
        return ResponseUtil.failed(error)
      }
    }

    @Post('dosen')
    async storeDosen(@Body() createDosenDto: CreateDosenDto[]) {
      try {
        const result = await this.dosenService.store(createDosenDto)
        return ResponseUtil.success()
      } catch(error) {
        return ResponseUtil.failed(error)
      }
    }

    @Post('prodi')
    async storeProdi(@Body() createProgramStudiDto: CreateProgramStudiDto[]) {
      try {
        await validateOrReject(createProgramStudiDto);
        await this.programStudiService.store(createProgramStudiDto)
        
        return ResponseUtil.success()
      } catch(error) {
        return ResponseUtil.failed(error)
      }
    }

    @Post('periode')
    async storePeriode(@Body() createTahunAkademikDto: CreateTahunAkademikDto[]) {
      try {
        await validateOrReject(createTahunAkademikDto)
        await this.tahunAkademikService.store(createTahunAkademikDto)

        return ResponseUtil.success()
      } catch (error) {
        return ResponseUtil.failed(error)
      }
    }

  @Post('kurikulum')    
  async storeKurikulum(@Body() createKurikulumDto: CreateKurikulumDto[]) {
    try {
      await validateOrReject(createKurikulumDto)
      await this.kurikulumService.store(createKurikulumDto)
      return ResponseUtil.success()
    } catch (error) {
      return ResponseUtil.failed(error)
    }
  }

  @Put('kurikulum')
  async UpdateKurikulum(@Body() updateKurikulumFeeder : UpdateKurikulumFeeder) {
    try {
     await this.kurikulumBerlakuService.updateRefFeeder(updateKurikulumFeeder)
      return ResponseUtil.success()
    } catch(error) {
      return ResponseUtil.failed(error)
    }
  }

  @Get('prodi/list')
  async getProdi() :Promise<ResponseUtil> {
    try {
      const result = await this.programStudiService.list()

      return ResponseUtil.success(result)
    } catch(error) {
      return ResponseUtil.failed(error)
    }
  }

  @Post('kelas')
  async storeKelas(@Body() createKelasFeederDto: CreateKelasFeederDto[] ) {
    try {
      await validateOrReject(createKelasFeederDto)
      await this.kelasService.store(createKelasFeederDto)
      
      return ResponseUtil.success()
    } catch(error) {
      return ResponseUtil.failed(error)
    }
  }

  @Get('kelas/list')
  async getKelas(@Query() query: QueryKelasDto) {
    try {
      const result = await this.kelasService.getKelas(query)

      return ResponseUtil.success(result)
    } catch(error) {
      return ResponseUtil.failed(error)
    }
  }

  @Post('krs')
  async storeKrs(@Body() createKRSFeederDto: CreateKRSFeederDto[]) {
    try {
      const result = await this.krsService.storeKrs(createKRSFeederDto)

      return ResponseUtil.success(result)
    } catch (error) {
      return ResponseUtil.failed(error)
    }
  }

  @Get('krs/list')
  async getKrs() {
    try {
      const result = await this.enrollmentService.getKrs()

      return ResponseUtil.success(result)
    } catch(error) {
      return ResponseUtil.failed(error)
    }
  }

}