import { Controller, Get } from '@nestjs/common';
import { Public } from './modules/auth/auth.decorator';

@Controller('health')
export class HealthController {
  @Get()
  @Public()
  healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
    };
  }
}
