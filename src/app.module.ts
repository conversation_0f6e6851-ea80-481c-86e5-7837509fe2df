import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './modules/user/user.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { DataSource } from 'typeorm';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MataKuliahKurikulumModule } from './modules/mata-kuliah-kurikulum/mata-kuliah-kurikulum.module';
import { KurikulumModule } from './modules/kurikulum/kurikulum.module';
import { MataKuliahModule } from './modules/mata-kuliah/mata-kuliah.module';
import { MataKuliahPrasyaratModule } from './modules/mata-kuliah-prasyarat/mata-kuliah-prasyarat.module';
import { AuthModule } from './modules/auth/auth.module';
import { APP_GUARD } from '@nestjs/core';
import { KampusModule } from './modules/kampus/kampus.module';
import { SubstansiMataKuliahModule } from './modules/substansi-mata-kuliah/substansi-mata-kuliah.module';
import { BobotNilaiModule } from './modules/bobot-nilai/bobot-nilai.module';
import { DosenModule } from './modules/dosen/dosen.module';
import { MailerModule } from '@nestjs-modules/mailer';
import { CaslModule } from './modules/casl/casl.module';
import { KelasModule } from './modules/kelas/kelas.module';
import { MahasiswaModule } from './modules/mahasiswa/mahasiswa.module';
import { JadwalModule } from './modules/jadwal/jadwal.module';
import { SetKurikulumModule } from './modules/set-kurikulum/set-kurikulum.module';
import { ProgramStudiModule } from './modules/program-studi/program-studi.module';
import { TahunAkademikModule } from './modules/tahun-akademik/tahun-akademik.module';
import { FakultasModule } from './modules/fakultas/fakultas.module';
import { JurusanModule } from './modules/jurusan/jurusan.module';
import { KalenderAkademikModule } from './modules/kalender-akademik/kalender-akademik.module';
import { CutiMahasiswaModule } from './modules/cuti-mahasiswa/cuti-mahasiswa.module';
import { NilaiKonversiModule } from './modules/nilai-konversi/nilai-konversi.module';
import { TugasAkhirModule } from './modules/tugas-akhir/tugas-akhir.module';
import { ConfigModule } from '@nestjs/config';
import { DraftSkModule } from './modules/draft-sk/draft-sk.module';
import { MenuModule } from './modules/menu/menu.module';
import { KuesionerModule } from './modules/kuesioner/kuesioner.module';
import { BimbinganTugasAkhirModule } from './modules/bimbingan-tugas-akhir/bimbingan-tugas-akhir.module';
import { PembimbingAkademikModule } from './modules/pembimbing-akademik/pembimbing-akademik.module';
import { KrsModule } from './modules/krs/krs.module';
import { ApiKeyGuard } from './modules/auth/api-key.guard';
import { LembarPengesahanModule } from './modules/lembar-pengesahan/lembar-pengesahan.module';
import { MulterModule } from '@nestjs/platform-express';
import { EnumController } from './enum.controller';
import { AspekPenilaianModule } from './modules/aspek-penilaian/aspek-penilaian.module';
import { EnrollmentModule } from './modules/enrollment/enrollment.module';
import { ProgramMahasiswaModule } from './modules/program-mahasiswa/program-mahasiswa.module';
import { NotifikasiModule } from './modules/notifikasi/notifikasi.module';
import { AbsensiModule } from './modules/absensi/absensi.module';
import { FeederModule } from './modules/feeder/feeder.module';
import { doubleCsrf } from 'csrf-csrf';
// import { RedisModule } from '@liaoliaots/nestjs-redis';
import { FinanceModule } from './modules/finance/finance.module';
import { JabatanModule } from './modules/jabatan/jabatan.module';
import { DosenStrukturalModule } from './modules/dosen-struktural/dosen-struktural.module';
import { DosenFungsionalModule } from './modules/dosen-fungsional/dosen-fungsional.module';
import { GeneratepdfModule } from './modules/generatepdf/generatepdf.module';

@Module({
  imports: [
    // RedisModule.forRoot({
    //   config: {
    //     host: process.env.REDIS_HOST,
    //     port: 6379,
    //     password: process.env.REDIS_PASS
    //   }
    // }),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MailerModule.forRoot({
      transport: {
        host: 'smtp.gmail.com',
        port: 465,
        ignoreTLS: true,
        secure: true,
        auth: {
          user: process.env.EMAIL_SUPPORT_USER,
          pass: process.env.EMAIL_SUPPORT_PASS,
        },
      },
      defaults: {
        from: '"nest-modules" <<EMAIL>>',
      },
    }),
    MulterModule.register({
      dest: './files',
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      username: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      entities: ['dist/**/*.entity{.ts,.js}'],
      synchronize: false,
    }),

    UserModule,
    DashboardModule,
    MataKuliahKurikulumModule,
    KurikulumModule,
    MataKuliahModule,
    MataKuliahPrasyaratModule,
    AuthModule,
    SubstansiMataKuliahModule,
    BobotNilaiModule,
    DosenModule,
    CaslModule,
    KelasModule,
    MahasiswaModule,
    JadwalModule,
    SetKurikulumModule,
    KampusModule,
    FakultasModule,
    JurusanModule,
    ProgramStudiModule,
    TahunAkademikModule,
    KalenderAkademikModule,
    CutiMahasiswaModule,
    NilaiKonversiModule,
    TugasAkhirModule,
    DraftSkModule,
    KuesionerModule,
    MenuModule,
    BimbinganTugasAkhirModule,
    PembimbingAkademikModule,
    BobotNilaiModule,
    KrsModule,
    LembarPengesahanModule,
    AspekPenilaianModule,
    EnrollmentModule,
    ProgramMahasiswaModule,
    NotifikasiModule,
    AbsensiModule,
    FeederModule,
    FinanceModule,
    JabatanModule,
    DosenStrukturalModule,
    DosenFungsionalModule,
    GeneratepdfModule,
  ],
  controllers: [AppController, EnumController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: ApiKeyGuard,
    },
  ],
})
export class AppModule {
  constructor(private dataSource: DataSource) {}
}
