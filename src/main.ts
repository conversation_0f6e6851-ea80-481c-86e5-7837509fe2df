import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { AppModule } from './app.module';
import { AuthFilter } from './modules/auth/auth.filter';
import { AuthLoginFilter } from './modules/auth/auth-login.filter';
import { RequestMethod, ValidationPipe } from '@nestjs/common';
import { ForcePasswordChangeFilter } from './modules/auth/force-password-change.filter';
import { NotFoundExceptionFilter } from './modules/auth/auth-not-found.filter';
import { WrongCredentialFilter } from './modules/auth/auth-wrong-credential.filter';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import * as cookieParser from 'cookie-parser';
import { doubleCsrf } from 'csrf-csrf';
import * as hbs from 'hbs';
import * as session from 'express-session';
import * as fs from 'fs';
import { ConfigService } from '@nestjs/config';
import * as express from 'express';
import 'dotenv/config'

const redis = require('redis');

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const configService = app.get(ConfigService);
  const port = configService.get('APP_PORT');
  const hbsutils = require('hbs-utils')(hbs)
  app.use(express.json({limit: '50mb'}));
  app.use(express.urlencoded({ limit: '50mb', extended: true }));
  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.useStaticAssets(join(__dirname, '..', 'storage'));
  app.setBaseViewsDir(join(__dirname, '..', 'views'));

  app.enableCors();

  app.setViewEngine('hbs');
  app.useGlobalInterceptors(new LoggingInterceptor());
  app.useGlobalFilters(new AuthFilter());
  app.useGlobalFilters(new AuthLoginFilter());
  app.useGlobalFilters(new ForcePasswordChangeFilter());
  app.useGlobalFilters(new NotFoundExceptionFilter());
  app.useGlobalFilters(new WrongCredentialFilter());
  app.useGlobalPipes(
    new ValidationPipe({ whitelist: true, enableDebugMessages: true }),
  );
  app.use(cookieParser());

  //redis setup
  const RedisStore = require('connect-redis').default;
  const redisClient = redis.createClient({
    url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
  });

  await redisClient.connect();
  redisClient.on('error', (err) =>
    console.log('Could not establish a connection with redis. ' + err),
  );
  redisClient.on('connect', () =>
    console.log('Connected to redis successfully'),
  );

  app.use(
    session({
      store: new RedisStore({ client: redisClient as any }),
      secret: process.env.REDIS_SECRET,
      resave: false,
      saveUninitialized: false,
      cookie: {
        httpOnly: true,
        sameSite: 'lax',
        //secure: true,
      },
    }),
  );
  //end redis setup

  // CSRF Protection setup
  const { generateToken } = doubleCsrf({
    getSecret: () => process.env.CSRF_SECRET || 'your-csrf-secret-key',
    getSessionIdentifier: (req: any) => req.sessionID || 'anonymous', // Use session ID or fallback
    cookieName: 'psifi.x-csrf-token', // Remove __Host- prefix for development
    cookieOptions: {
      httpOnly: true,
      sameSite: 'lax',
      secure: false, // set to true in production with HTTPS
    },
    size: 64,
    ignoredMethods: ['GET', 'HEAD', 'OPTIONS'],
    getTokenFromRequest: (req: any) => {
      const token = req.headers['csrf-token'];
      return Array.isArray(token) ? token[0] : token;
    },
  });

  // Add CSRF token to request object for views
  app.use((req: any, res: any, next: any) => {
    req.csrfToken = () => {
      return generateToken(req, res);
    };
    next();
  });

  app.setGlobalPrefix('api', {
    exclude: [
      'auth/(.*)',
      { path: '(.*)(/?)index', method: RequestMethod.GET },
    ],
  });

  hbs.registerPartials(join(__dirname, '..', 'views/partials'));
  hbsutils.registerPartials(join(__dirname, '..', 'views/partials'));
  hbsutils.registerWatchedPartials(join(__dirname, '..', 'views/partials'));
  hbsutils.precompilePartials();
  initHBSHelpers();

  const config = new DocumentBuilder()
    .setTitle('Siakad V4')
    .setDescription('Siakad V4 API')
    .setVersion('1.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);
  fs.mkdir('logs', { recursive: true }, (err) => {
    if (err) throw err;
  });
  fs.writeFile('logs/main.log', '', { flag: 'wx' }, function (err) {
    //if (err) throw err;
  });
  await app.listen('4000');
}

function initHBSHelpers() {
  hbs.registerHelper('inc', function (value, options) {
    return parseInt(value) + 1;
  });
  hbs.registerHelper('json', function (context) {
    return JSON.stringify(context);
  });

  hbs.registerHelper('ifCond', function (v1, v2, options) {
    if (v1 === v2) {
      return options.fn(this);
    }
    return options.inverse(this);
  });

  hbs.registerHelper('for', function (from, to, incr, block) {
    let accum = '';
    for (let i = from; i < to; i += incr) {
      accum += block.fn(i);
    }
    return accum;

  });
  hbs.registerHelper('ternary', function (condition, valueTrue, valueFalse) {
    return condition ? valueTrue : valueFalse;
  });
}

bootstrap();
